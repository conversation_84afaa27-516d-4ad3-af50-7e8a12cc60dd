import os

from sqlalchemy import exc
from sqlalchemy import create_engine
import pandas as pd
from app.logger.get_logger import log, logger


@log
def store_in_db(db_name: str, table_name: str, df: pd.DataFrame, exchange: str):
    """
    Stores a DataFrame in a SQLite database.

    Args:
        db_name (str): The name of the SQLite database.
        table_name (str): The name of the table in the database.
        df (pd.DataFrame): The DataFrame to be stored.
        exchange (str): The name of the exchange (used as subfolder).

    Returns:
        None
    """
    # Ensure db_name has .db extension
    if not db_name.endswith('.db'):
        db_name = db_name + '.db'

    db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', exchange, db_name)

    # Ensure the exchange directory exists
    os.makedirs(os.path.dirname(db_path), exist_ok=True)

    engine = create_engine(f'sqlite:///{db_path}')

    existing_df = _read_from_db(engine, table_name)
    df = pd.concat([existing_df, df])

    # Sort and remove duplicates
    df = df.sort_values(by = 'timestamp').drop_duplicates(subset = 'timestamp', keep = 'last')

    _write_to_db(engine, table_name, df, existing_df)

    return


@log
def _read_from_db(engine, table_name):
    """
    Reads existing table from database.

    If the table does not exist, skips reading and proceeds with writing.

    Parameters
    ----------
    engine : sqlalchemy engine
        to access the database
    table_name : str
        Name of the table to read from

    Returns
    -------
    pd.DataFrame
        Existing data in the table
    """
    try:
        # Read existing table from database
        existing_df = pd.read_sql_query(f"SELECT * FROM {table_name}", engine, parse_dates = ['date'])
    except exc.OperationalError:
        # If table does not exist, skip reading and proceed with writing
        existing_df = pd.DataFrame()
    return existing_df


@log
def _write_to_db(engine, table_name, df, existing_df):
    """
    Writes data to a database table. If the table does not exist, it creates a new one.
    If the table exists, it appends new data that does not have a matching timestamp
    in the existing data.

    Args:
        engine: SQLAlchemy engine connected to the database.
        table_name (str): The name of the table in the database.
        df (pd.DataFrame): DataFrame containing the data to be written to the database.
        existing_df (pd.DataFrame): DataFrame containing the existing data from the database table.
    """
    if existing_df.empty:
        # Set column types and validate data
        df = _validate_and_convert_data_types(df)

        # Table does not exist, create a new one
        df.to_sql(table_name, engine, if_exists = 'fail', index = False)
    else:
        # Table already exists, append new data to it
        temp_df = df[~df['timestamp'].isin(existing_df['timestamp'])].copy()
        # Set column types and validate data for new data
        temp_df = _validate_and_convert_data_types(temp_df)
        # Append new data to the existing table
        temp_df.to_sql(table_name, engine, if_exists = 'append', index = False)


@log
def _validate_and_convert_data_types(df: pd.DataFrame) -> pd.DataFrame:
    """
    Validate and convert DataFrame to consistent data types for database storage.

    Ensures all source data follows the same format:
    - timestamp: int64
    - date: datetime64[ns, UTC] (timezone-aware)
    - open, high, low, close, volume: float64

    Args:
        df: DataFrame to validate and convert

    Returns:
        DataFrame with consistent data types

    Raises:
        ValueError: If required columns are missing or conversion fails
    """
    if df.empty:
        return df

    # Required columns
    required_columns = ['timestamp', 'date', 'open', 'high', 'low', 'close', 'volume']

    # Check for required columns
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        raise ValueError(f"Missing required columns: {missing_columns}")

    # Create a copy to avoid modifying the original
    df_validated = df.copy()

    try:
        # Convert timestamp to int64
        df_validated['timestamp'] = pd.to_numeric(df_validated['timestamp'], errors='coerce').astype('int64')

        # Convert date to timezone-aware datetime (UTC)
        df_validated['date'] = pd.to_datetime(df_validated['date'])
        if df_validated['date'].dtype.tz is None:
            # If timezone-naive, assume UTC
            df_validated['date'] = df_validated['date'].dt.tz_localize('UTC')
        else:
            # If already timezone-aware, convert to UTC
            df_validated['date'] = df_validated['date'].dt.tz_convert('UTC')

        # Convert OHLC values to float64
        ohlc_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in ohlc_columns:
            df_validated[col] = pd.to_numeric(df_validated[col], errors='coerce').astype('float64')

        # Remove rows with NaN values (from failed conversions)
        initial_rows = len(df_validated)
        df_validated = df_validated.dropna()
        dropped_rows = initial_rows - len(df_validated)

        if dropped_rows > 0:
            logger.warning(f"Dropped {dropped_rows} rows due to data conversion issues")

        # Validate that we still have data
        if df_validated.empty:
            raise ValueError("All rows were dropped during data type conversion")

        return df_validated

    except Exception as e:
        logger.error(f"Data type validation failed: {str(e)}")
        raise ValueError(f"Failed to validate and convert data types: {str(e)}")
