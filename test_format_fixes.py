#!/usr/bin/env python3
"""
Test script to verify that source data format fixes are working correctly.
"""

import pandas as pd
import sys
import os

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

def test_bitstamp_format_fixed():
    """Test that Bitstamp now returns proper data types"""
    try:
        from app.sources.bitstamp.get_candles_from_bitstamp import get_candles_from_bitstamp
        
        print("=== TESTING BITSTAMP FORMAT FIXES ===")
        df = get_candles_from_bitstamp('btc-usd', 'h1', 3, True)
        
        if df is not None and not df.empty:
            print(f"✅ Data retrieved successfully")
            print(f"Columns: {list(df.columns)}")
            print(f"Data types:\n{df.dtypes}")
            
            # Check specific data types
            checks = {
                'timestamp': df['timestamp'].dtype == 'int64',
                'date_timezone': df['date'].dtype.tz is not None,
                'open_numeric': df['open'].dtype == 'float64',
                'high_numeric': df['high'].dtype == 'float64', 
                'low_numeric': df['low'].dtype == 'float64',
                'close_numeric': df['close'].dtype == 'float64',
                'volume_numeric': df['volume'].dtype == 'float64'
            }
            
            print("\nData type validation:")
            for check_name, passed in checks.items():
                status = "✅" if passed else "❌"
                print(f"  {status} {check_name}: {passed}")
            
            all_passed = all(checks.values())
            print(f"\n{'✅ ALL CHECKS PASSED' if all_passed else '❌ SOME CHECKS FAILED'}")
            
            return df, all_passed
        else:
            print("❌ No data returned from Bitstamp")
            return None, False
            
    except Exception as e:
        print(f"❌ Bitstamp test error: {str(e)}")
        return None, False

def test_kraken_format():
    """Test Kraken format (should already be correct)"""
    try:
        from app.sources.kraken.get_candles_from_kraken import get_candles_from_kraken
        
        print("\n=== TESTING KRAKEN FORMAT ===")
        df = get_candles_from_kraken('btc-usd', 'h1', 3, True)
        
        if df is not None and not df.empty:
            print(f"✅ Data retrieved successfully")
            print(f"Data types:\n{df.dtypes}")
            
            checks = {
                'timestamp': df['timestamp'].dtype == 'int64',
                'date_timezone': df['date'].dtype.tz is not None,
                'open_numeric': df['open'].dtype == 'float64',
                'volume_numeric': df['volume'].dtype == 'float64'
            }
            
            print("\nData type validation:")
            for check_name, passed in checks.items():
                status = "✅" if passed else "❌"
                print(f"  {status} {check_name}: {passed}")
            
            all_passed = all(checks.values())
            return df, all_passed
        else:
            print("❌ No data returned from Kraken")
            return None, False
            
    except Exception as e:
        print(f"❌ Kraken test error: {str(e)}")
        return None, False

def test_coindesk_format_fixed():
    """Test that Coindesk now has timezone-aware dates"""
    try:
        from app.sources.coindesk.get_candles_from_coindesk import get_candles_from_coindesk
        
        print("\n=== TESTING COINDESK FORMAT FIXES ===")
        df = get_candles_from_coindesk('btc-usd', 'h1', 3, True)
        
        if df is not None and not df.empty:
            print(f"✅ Data retrieved successfully")
            print(f"Data types:\n{df.dtypes}")
            
            checks = {
                'timestamp': df['timestamp'].dtype == 'int64',
                'date_timezone': df['date'].dtype.tz is not None,
                'open_numeric': df['open'].dtype == 'float64',
                'volume_numeric': df['volume'].dtype == 'float64'
            }
            
            print("\nData type validation:")
            for check_name, passed in checks.items():
                status = "✅" if passed else "❌"
                print(f"  {status} {check_name}: {passed}")
            
            all_passed = all(checks.values())
            return df, all_passed
        else:
            print("❌ No data returned from Coindesk")
            return None, False
            
    except Exception as e:
        print(f"❌ Coindesk test error: {str(e)}")
        return None, False

def test_database_storage():
    """Test that database storage validation works"""
    try:
        from app.db.sqlite.store_in_db import _validate_and_convert_data_types
        
        print("\n=== TESTING DATABASE STORAGE VALIDATION ===")
        
        # Create test data with mixed types (simulating old Bitstamp format)
        test_data = pd.DataFrame({
            'timestamp': ['1748606400', '1748602800'],  # strings
            'date': ['2025-05-30 12:00:00', '2025-05-30 11:00:00'],  # strings
            'open': ['105904', '105939'],  # strings
            'high': ['106015', '105997'],  # strings  
            'low': ['105164', '105722'],   # strings
            'close': ['105714', '105908'], # strings
            'volume': ['30.96', '11.84']   # strings
        })
        
        print("Before validation:")
        print(f"Data types:\n{test_data.dtypes}")
        
        # Apply validation
        validated_data = _validate_and_convert_data_types(test_data)
        
        print("\nAfter validation:")
        print(f"Data types:\n{validated_data.dtypes}")
        
        # Check results
        checks = {
            'timestamp_int64': validated_data['timestamp'].dtype == 'int64',
            'date_timezone_aware': validated_data['date'].dtype.tz is not None,
            'open_float64': validated_data['open'].dtype == 'float64',
            'volume_float64': validated_data['volume'].dtype == 'float64'
        }
        
        print("\nValidation checks:")
        for check_name, passed in checks.items():
            status = "✅" if passed else "❌"
            print(f"  {status} {check_name}: {passed}")
        
        all_passed = all(checks.values())
        print(f"\n{'✅ DATABASE VALIDATION PASSED' if all_passed else '❌ DATABASE VALIDATION FAILED'}")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Database validation test error: {str(e)}")
        return False

def main():
    """Run all format tests"""
    print("Testing source data format fixes...\n")
    
    # Test each source
    bitstamp_df, bitstamp_ok = test_bitstamp_format_fixed()
    kraken_df, kraken_ok = test_kraken_format()
    coindesk_df, coindesk_ok = test_coindesk_format_fixed()
    
    # Test database validation
    db_validation_ok = test_database_storage()
    
    # Summary
    print("\n" + "="*50)
    print("SUMMARY OF FORMAT FIXES")
    print("="*50)
    
    results = {
        'Bitstamp fixes': bitstamp_ok,
        'Kraken format': kraken_ok, 
        'Coindesk fixes': coindesk_ok,
        'Database validation': db_validation_ok
    }
    
    for test_name, passed in results.items():
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    all_tests_passed = all(results.values())
    print(f"\nOverall result: {'✅ ALL TESTS PASSED' if all_tests_passed else '❌ SOME TESTS FAILED'}")
    
    if all_tests_passed:
        print("\n🎉 Source data format consistency has been achieved!")
        print("All sources now produce compatible data formats for database storage.")
    else:
        print("\n⚠️  Some issues remain. Check the failed tests above.")

if __name__ == "__main__":
    main()
