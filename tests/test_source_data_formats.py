"""
Test source data format consistency.

This module tests that all sources (Bitstamp, Kraken, Coindesk) return data
in consistent formats that are compatible with the unified database system.
"""

import pytest
import pandas as pd
from unittest.mock import patch, MagicMock

from app.sources.bitstamp.get_candles_from_bitstamp import get_candles_from_bitstamp
from app.sources.kraken.get_candles_from_kraken import get_candles_from_kraken
from app.sources.coindesk.get_candles_from_coindesk import get_candles_from_coindesk
from app.db.sqlite.store_in_db import _validate_and_convert_data_types


class TestSourceDataFormats:
    """Test data format consistency across all sources."""
    
    def test_bitstamp_data_format(self):
        """Test that Bitstamp returns properly formatted data."""
        try:
            df = get_candles_from_bitstamp('btc-usd', 'h1', 3, True)
            
            if df is not None and not df.empty:
                # Check columns
                expected_columns = ['timestamp', 'date', 'open', 'high', 'low', 'close', 'volume']
                assert list(df.columns) == expected_columns
                
                # Check data types
                assert df['timestamp'].dtype == 'int64'
                assert df['date'].dtype.tz is not None  # timezone-aware
                assert df['open'].dtype == 'float64'
                assert df['high'].dtype == 'float64'
                assert df['low'].dtype == 'float64'
                assert df['close'].dtype == 'float64'
                assert df['volume'].dtype == 'float64'
                
        except Exception as e:
            pytest.skip(f"Bitstamp API unavailable: {str(e)}")
    
    def test_kraken_data_format(self):
        """Test that Kraken returns properly formatted data."""
        try:
            df = get_candles_from_kraken('btc-usd', 'h1', 3, True)
            
            if df is not None and not df.empty:
                # Check columns
                expected_columns = ['timestamp', 'date', 'open', 'high', 'low', 'close', 'volume']
                assert list(df.columns) == expected_columns
                
                # Check data types
                assert df['timestamp'].dtype == 'int64'
                assert df['date'].dtype.tz is not None  # timezone-aware
                assert df['open'].dtype == 'float64'
                assert df['high'].dtype == 'float64'
                assert df['low'].dtype == 'float64'
                assert df['close'].dtype == 'float64'
                assert df['volume'].dtype == 'float64'
                
        except Exception as e:
            pytest.skip(f"Kraken API unavailable: {str(e)}")
    
    def test_coindesk_data_format(self):
        """Test that Coindesk returns properly formatted data."""
        try:
            df = get_candles_from_coindesk('btc-usd', 'h1', 3, True)
            
            if df is not None and not df.empty:
                # Check columns
                expected_columns = ['timestamp', 'date', 'open', 'high', 'low', 'close', 'volume']
                assert list(df.columns) == expected_columns
                
                # Check data types
                assert df['timestamp'].dtype == 'int64'
                assert df['date'].dtype.tz is not None  # timezone-aware
                assert df['open'].dtype == 'float64'
                assert df['high'].dtype == 'float64'
                assert df['low'].dtype == 'float64'
                assert df['close'].dtype == 'float64'
                assert df['volume'].dtype == 'float64'
                
        except Exception as e:
            pytest.skip(f"Coindesk API unavailable: {str(e)}")
    
    def test_database_validation_function(self):
        """Test that database validation function works correctly."""
        # Create test data with mixed types (simulating problematic data)
        test_data = pd.DataFrame({
            'timestamp': ['1748606400', '1748602800'],  # strings
            'date': ['2025-05-30 12:00:00', '2025-05-30 11:00:00'],  # strings
            'open': ['105904.50', '105939.25'],  # strings
            'high': ['106015.75', '105997.80'],  # strings  
            'low': ['105164.25', '105722.50'],   # strings
            'close': ['105714.00', '105908.75'], # strings
            'volume': ['30.96393019', '11.84393529']   # strings
        })
        
        # Apply validation
        validated_data = _validate_and_convert_data_types(test_data)
        
        # Check that validation worked
        assert not validated_data.empty
        assert len(validated_data) == 2
        
        # Check data types after validation
        assert validated_data['timestamp'].dtype == 'int64'
        assert validated_data['date'].dtype.tz is not None  # timezone-aware
        assert validated_data['open'].dtype == 'float64'
        assert validated_data['high'].dtype == 'float64'
        assert validated_data['low'].dtype == 'float64'
        assert validated_data['close'].dtype == 'float64'
        assert validated_data['volume'].dtype == 'float64'
        
        # Check that values were converted correctly
        assert validated_data['timestamp'].iloc[0] == 1748606400
        assert validated_data['open'].iloc[0] == 105904.50
        assert validated_data['volume'].iloc[0] == 30.96393019
    
    def test_validation_with_missing_columns(self):
        """Test that validation fails appropriately with missing columns."""
        # Create test data missing required columns
        incomplete_data = pd.DataFrame({
            'timestamp': ['1748606400'],
            'open': ['105904.50']
            # Missing: date, high, low, close, volume
        })
        
        with pytest.raises(ValueError, match="Missing required columns"):
            _validate_and_convert_data_types(incomplete_data)
    
    def test_validation_with_invalid_data(self):
        """Test that validation handles invalid data gracefully."""
        # Create test data with invalid values
        invalid_data = pd.DataFrame({
            'timestamp': ['invalid_timestamp'],
            'date': ['invalid_date'],
            'open': ['not_a_number'],
            'high': ['also_not_a_number'],
            'low': ['still_not_a_number'],
            'close': ['definitely_not_a_number'],
            'volume': ['nope_not_a_number']
        })
        
        with pytest.raises(ValueError, match="All rows were dropped during data type conversion"):
            _validate_and_convert_data_types(invalid_data)
    
    def test_validation_with_empty_dataframe(self):
        """Test that validation handles empty DataFrames."""
        empty_df = pd.DataFrame()
        result = _validate_and_convert_data_types(empty_df)
        assert result.empty
    
    def test_cross_source_format_consistency(self):
        """Test that all sources return data in the same format."""
        sources_data = {}
        
        # Try to get data from each source
        try:
            sources_data['bitstamp'] = get_candles_from_bitstamp('btc-usd', 'h1', 2, True)
        except:
            pass
            
        try:
            sources_data['kraken'] = get_candles_from_kraken('btc-usd', 'h1', 2, True)
        except:
            pass
            
        try:
            sources_data['coindesk'] = get_candles_from_coindesk('btc-usd', 'h1', 2, True)
        except:
            pass
        
        # Filter out None/empty results
        valid_sources = {name: df for name, df in sources_data.items() 
                        if df is not None and not df.empty}
        
        if len(valid_sources) < 2:
            pytest.skip("Not enough sources available for comparison")
        
        # Compare formats across sources
        reference_columns = None
        reference_dtypes = None
        
        for source_name, df in valid_sources.items():
            if reference_columns is None:
                reference_columns = list(df.columns)
                reference_dtypes = {col: df[col].dtype for col in df.columns}
            else:
                # Check columns match
                assert list(df.columns) == reference_columns, \
                    f"{source_name} has different columns than reference"
                
                # Check data types match (with some flexibility for timezone representation)
                for col in df.columns:
                    if col == 'date':
                        # Both should be timezone-aware
                        assert df[col].dtype.tz is not None, \
                            f"{source_name} {col} is not timezone-aware"
                        assert reference_dtypes[col].tz is not None, \
                            f"Reference {col} is not timezone-aware"
                    else:
                        assert df[col].dtype == reference_dtypes[col], \
                            f"{source_name} {col} dtype {df[col].dtype} != reference {reference_dtypes[col]}"


if __name__ == "__main__":
    pytest.main([__file__])
